import { Canvas, Rect } from "fabric";
import { CropData } from "@/shared/types";
import { loadCanvasImage } from "../rendering/image";

export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

export const applyCropToCanvas = (
  canvas: Canvas,
  cropData: CropData,
  containerRect?: DOMRect
): void => {
  if (!cropData.normalizedCropRect) return;

  const backgroundImage = canvas.backgroundImage;
  if (!backgroundImage) return;

  const originalWidth = backgroundImage.width || 512;
  const originalHeight = backgroundImage.height || 512;
  const scale = backgroundImage.scaleX || 1;
  const scaledWidth = originalWidth * scale;
  const scaledHeight = originalHeight * scale;

  const left = cropData.normalizedCropRect.left * scaledWidth;
  const top = cropData.normalizedCropRect.top * scaledHeight;
  const width = cropData.normalizedCropRect.width * scaledWidth;
  const height = cropData.normalizedCropRect.height * scaledHeight;

  const viewportContainer = cropData.canvasDimensions || containerRect;
  if (viewportContainer) {
    const cropScale = Math.min(viewportContainer.width / width, viewportContainer.height / height);
    const actualWidth = width * cropScale;
    const actualHeight = height * cropScale;

    canvas.setDimensions({ width: actualWidth, height: actualHeight });

    const vpt: [number, number, number, number, number, number] = [
      cropScale,
      0,
      0,
      cropScale,
      -left * cropScale,
      -top * cropScale,
    ];

    canvas.setViewportTransform(vpt);
  }

  canvas.renderAll();
};

export const handleCropOperation = (
  fabricCanvas: React.RefObject<Canvas | null>,
  hasPerformedCrop: boolean,
  setCropData: (data: CropData) => void,
  isUndoing: React.MutableRefObject<boolean>,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  return async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(
        canvas,
        setCropData,
        setHasPerformedCrop,
        containerRef,
        originalImageUrl
      );
    }

    let cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      cropRect = canvas.getObjects().find((obj) => (obj as any).name === "cropRect");
    }

    if (!cropRect || !canvas.backgroundImage) return;

    isUndoing.current = true;

    const left = cropRect.left || 0;
    const top = cropRect.top || 0;
    const width = (cropRect.width || 0) * (cropRect.scaleX || 1);
    const height = (cropRect.height || 0) * (cropRect.scaleY || 1);

    cropRect.set({ visible: false });
    canvas.remove(cropRect);
    canvas.discardActiveObject();

    const backgroundImage = canvas.backgroundImage;
    if (!backgroundImage) return;

    const imageWidth = backgroundImage.width || 512;
    const imageHeight = backgroundImage.height || 512;
    const imageScaleX = backgroundImage.scaleX || 1;
    const imageScaleY = backgroundImage.scaleY || 1;

    const renderedImageWidth = imageWidth * imageScaleX;
    const renderedImageHeight = imageHeight * imageScaleY;

    const backgroundImageLeft = backgroundImage.left || 0;
    const backgroundImageTop = backgroundImage.top || 0;
    const originX = (backgroundImage as any).originX || "left";
    const originY = (backgroundImage as any).originY || "top";

    let imageLeft = backgroundImageLeft;
    let imageTop = backgroundImageTop;

    if (originX === "center") {
      imageLeft = backgroundImageLeft - renderedImageWidth / 2;
    }
    if (originY === "center") {
      imageTop = backgroundImageTop - renderedImageHeight / 2;
    }

    let relativeLeft = left - imageLeft;
    let relativeTop = top - imageTop;

    // Account for background image rotation
    const bgAngle = backgroundImage.angle || 0;
    if (bgAngle !== 0) {
      // Transform crop coordinates based on rotation
      const centerX = renderedImageWidth / 2;
      const centerY = renderedImageHeight / 2;

      // Translate to origin
      const translatedX = relativeLeft - centerX;
      const translatedY = relativeTop - centerY;

      // Apply inverse rotation to get original coordinates
      const angleRad = (-bgAngle * Math.PI) / 180;
      const cos = Math.cos(angleRad);
      const sin = Math.sin(angleRad);

      relativeLeft = translatedX * cos - translatedY * sin + centerX;
      relativeTop = translatedX * sin + translatedY * cos + centerY;
    }

    const normalizedCropRect = {
      left: relativeLeft / renderedImageWidth,
      top: relativeTop / renderedImageHeight,
      width: width / renderedImageWidth,
      height: height / renderedImageHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };

    applyCropToCanvas(canvas, cropResult, containerRef?.current?.getBoundingClientRect());

    setCropData(cropResult);
    setHasPerformedCrop(true);
    isUndoing.current = false;
  };
};

export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>,
  originalImageUrl?: React.MutableRefObject<string>
) => {
  if (!canvas) return;

  canvas.clipPath = undefined;
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

  if (containerRef?.current) {
    const containerBounds = containerRef.current.getBoundingClientRect();
    canvas.setDimensions({ width: containerBounds.width, height: containerBounds.height });
  }

  if (originalImageUrl?.current) {
    const containerRect = containerRef?.current?.getBoundingClientRect();
    await loadCanvasImage(canvas, originalImageUrl.current, {
      containerRect: containerRect || undefined,
    });
  }

  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });

  setHasPerformedCrop(false);

  canvas.renderAll();
};
