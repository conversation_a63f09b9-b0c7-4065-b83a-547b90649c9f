import { Canvas } from "fabric";
import { CropData } from "@/shared/types";

export const createImageLoadContainer = (
  cropData: CropData,
  fallbackRect?: DOMRect
): DOMRect | undefined => {
  if (!cropData.canvasDimensions) return fallbackRect;

  return {
    width: cropData.canvasDimensions.width,
    height: cropData.canvasDimensions.height,
    x: 0,
    y: 0,
    top: 0,
    left: 0,
    bottom: cropData.canvasDimensions.height,
    right: cropData.canvasDimensions.width,
    toJSON: () => ({}),
  } as DOMRect;
};

export const applyCropToCanvas = (
  canvas: Canvas,
  cropData: CropData,
  containerRect?: DOMRect
): void => {
  if (!cropData.normalizedCropRect) return;

  const backgroundImage = canvas.backgroundImage;
  if (!backgroundImage) return;

  const originalWidth = backgroundImage.width || 512;
  const originalHeight = backgroundImage.height || 512;
  const scale = backgroundImage.scaleX || 1;

  const scaledWidth = originalWidth * scale;
  const scaledHeight = originalHeight * scale;

  const left = cropData.normalizedCropRect.left * scaledWidth;
  const top = cropData.normalizedCropRect.top * scaledHeight;
  const width = cropData.normalizedCropRect.width * scaledWidth;
  const height = cropData.normalizedCropRect.height * scaledHeight;

  const viewportContainer = cropData.canvasDimensions || containerRect;
  if (viewportContainer) {
    const cropScale = Math.min(viewportContainer.width / width, viewportContainer.height / height);
    const actualWidth = width * cropScale;
    const actualHeight = height * cropScale;

    canvas.setDimensions({ width: actualWidth, height: actualHeight });

    const vpt: [number, number, number, number, number, number] = [
      cropScale,
      0,
      0,
      cropScale,
      -left * cropScale,
      -top * cropScale,
    ];

    canvas.setViewportTransform(vpt);
  }

  canvas.renderAll();
};

export const restoreCroppedCanvas = async (
  canvas: Canvas,
  setCropData: (data: CropData) => void,
  setHasPerformedCrop: (value: boolean) => void,
  containerRef?: React.RefObject<HTMLElement | null>
) => {
  if (!canvas) return;

  const currentBackgroundImage = canvas.backgroundImage;
  const currentAngle = currentBackgroundImage?.angle || 0;
  const currentFlipX = currentBackgroundImage?.flipX || false;
  const currentFlipY = currentBackgroundImage?.flipY || false;
  const currentScaleX = currentBackgroundImage?.scaleX || 1;
  const currentScaleY = currentBackgroundImage?.scaleY || 1;

  canvas.clipPath = undefined;
  canvas.setViewportTransform([1, 0, 0, 1, 0, 0]);

  if (containerRef?.current && currentBackgroundImage) {
    const containerBounds = containerRef.current.getBoundingClientRect();

    const imageWidth = currentBackgroundImage.width || 512;
    const imageHeight = currentBackgroundImage.height || 512;

    let displayWidth = imageWidth * currentScaleX;
    let displayHeight = imageHeight * currentScaleY;

    if (Math.abs(currentAngle) === 90 || Math.abs(currentAngle) === 270) {
      [displayWidth, displayHeight] = [displayHeight, displayWidth];
    }

    const scale = Math.min(
      containerBounds.width / displayWidth,
      containerBounds.height / displayHeight
    );

    const canvasWidth = displayWidth * scale;
    const canvasHeight = displayHeight * scale;

    canvas.setDimensions({ width: canvasWidth, height: canvasHeight });

    currentBackgroundImage.set({
      left: canvasWidth / 2,
      top: canvasHeight / 2,
      originX: "center",
      originY: "center",
      angle: currentAngle,
      flipX: currentFlipX,
      flipY: currentFlipY,
      scaleX: currentScaleX * scale,
      scaleY: currentScaleY * scale,
    });

    canvas.backgroundImage = currentBackgroundImage;
  }

  setCropData({
    isCropped: false,
    normalizedCropRect: undefined,
    canvasDimensions: undefined,
  });

  setHasPerformedCrop(false);

  canvas.renderAll();
};
