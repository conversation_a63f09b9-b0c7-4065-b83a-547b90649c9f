import { useRef, useState } from "react";
import { Canvas } from "fabric";
import { UndoAction, UndoTrackingState, FabricMeasurementLine } from "@/shared/types";

export const useUndoTracking = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialObjectCount: React.MutableRefObject<number>
): UndoTrackingState => {
  const [undoStack, setUndoStack] = useState<UndoAction[]>([]);
  const isUndoingRef = useRef<boolean>(false);

  const handleUndo = async () => {
    if (!fabricCanvas?.current || !undoStack.length) return;

    const canvas = fabricCanvas.current;
    const action = undoStack[undoStack.length - 1];

    if (action.type === "crop") {
      setUndoStack((prev) => prev.slice(0, -1));
      return;
    }

    isUndoingRef.current = true;

    if (action.type === "add") {
      const objects = canvas.getObjects();
      if (objects.length > initialObjectCount.current) {
        canvas.remove(objects[objects.length - 1]);
        canvas.renderAll();
      }
    } else if (action.type === "add-measurement") {
      const lineObj = canvas
        .getObjects()
        .find(
          (obj) => (obj as unknown as { id: string }).id === action.lineId
        ) as FabricMeasurementLine;
      if (lineObj) {
        canvas.remove(lineObj);
        if (lineObj.measurementText) {
          canvas.remove(lineObj.measurementText);
        }
      }
      canvas.renderAll();
    } else if (action.type === "modify") {
      const obj = canvas
        .getObjects()
        .find((o) => (o as unknown as { id: string }).id === action.objectId);
      if (obj && action.previousState) {
        obj.set(action.previousState);
        obj.setCoords();
        canvas.renderAll();
      }
    }
    setUndoStack((prev) => prev.slice(0, -1));
    isUndoingRef.current = false;
  };

  const disableUndoTracking = () => {
    isUndoingRef.current = true;
  };
  const enableUndoTracking = () => {
    isUndoingRef.current = false;
  };

  return {
    undoStack,
    canUndo: undoStack.length > 0,
    isUndoingRef,
    handleUndo,
    addUndoAction: (action: UndoAction) => {
      if (isUndoingRef.current) return;
      setUndoStack((prev) => [...prev, action]);
    },
    disableUndoTracking,
    enableUndoTracking,
  };
};
