import { useState } from "react";
import { Canvas, Rect } from "fabric";
import { CropData, CropManagementState, UndoTrackingState } from "@/shared/types";
import { applyCropToCanvas, restoreCroppedCanvas } from "@/lib/fabric/operations/crop";

export const useCropManagement = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialCropData: CropData,
  undoTracking: UndoTrackingState,
  containerRef?: React.RefObject<HTMLElement | null>
): CropManagementState => {
  const [cropData, setCropData] = useState<CropData>(initialCropData);
  const [hasPerformedCrop, setHasPerformedCrop] = useState(initialCropData.isCropped || false);

  const handleCrop = async () => {
    if (!fabricCanvas?.current) return;
    const canvas = fabricCanvas.current;

    if (hasPerformedCrop) {
      return await restoreCroppedCanvas(canvas, setCropData, setHasPerformedCrop, containerRef);
    }

    const cropRect = canvas.getActiveObject();
    if (!(cropRect instanceof Rect) || (cropRect as any).name !== "cropRect") {
      return;
    }

    canvas.remove(cropRect);
    const left = cropRect.left!;
    const top = cropRect.top!;
    const width = cropRect.width! * cropRect.scaleX!;
    const height = cropRect.height! * cropRect.scaleY!;

    const backgroundImage = canvas.backgroundImage;
    if (!backgroundImage) return;

    const imageWidth = backgroundImage.width || 512;
    const imageHeight = backgroundImage.height || 512;
    const imageScaleX = backgroundImage.scaleX || 1;
    const imageScaleY = backgroundImage.scaleY || 1;

    const renderedImageWidth = imageWidth * imageScaleX;
    const renderedImageHeight = imageHeight * imageScaleY;

    const backgroundImageLeft = backgroundImage.left || 0;
    const backgroundImageTop = backgroundImage.top || 0;
    const originX = (backgroundImage as any).originX || "left";
    const originY = (backgroundImage as any).originY || "top";

    let imageLeft = backgroundImageLeft;
    let imageTop = backgroundImageTop;

    if (originX === "center") {
      imageLeft = backgroundImageLeft - renderedImageWidth / 2;
    }
    if (originY === "center") {
      imageTop = backgroundImageTop - renderedImageHeight / 2;
    }

    const bgAngle = backgroundImage.angle || 0;
    if (bgAngle === 90 || bgAngle === 270) {
      const leftAdjustment = Math.abs(imageLeft) < 100 ? imageLeft : 0;
      const topAdjustment = Math.abs(imageTop) < 100 ? imageTop : 0;
      imageLeft = imageLeft - leftAdjustment;
      imageTop = imageTop - topAdjustment;
    }

    const relativeLeft = left - imageLeft;
    const relativeTop = top - imageTop;

    console.log("Crop Debug:", {
      bgAngle: backgroundImage.angle,
      cropRect: { left, top, width, height },
      imagePos: { imageLeft, imageTop },
      relative: { relativeLeft, relativeTop },
      imageDims: { renderedImageWidth, renderedImageHeight },
    });

    const normalizedCropRect = {
      left: relativeLeft / renderedImageWidth,
      top: relativeTop / renderedImageHeight,
      width: width / renderedImageWidth,
      height: height / renderedImageHeight,
    };

    const cropResult: CropData = {
      isCropped: true,
      normalizedCropRect,
      canvasDimensions: containerRef?.current
        ? {
            width: containerRef.current.getBoundingClientRect().width,
            height: containerRef.current.getBoundingClientRect().height,
          }
        : undefined,
    };

    applyCropToCanvas(canvas, cropResult, containerRef?.current?.getBoundingClientRect());

    setCropData(cropResult);
    setHasPerformedCrop(true);
    undoTracking.isUndoingRef.current = false;
  };

  return {
    cropData,
    setCropData,
    hasPerformedCrop,
    setHasPerformedCrop,
    handleCrop,
  };
};
