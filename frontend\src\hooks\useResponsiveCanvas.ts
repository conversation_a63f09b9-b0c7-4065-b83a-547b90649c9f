import { Canvas } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps, CropData } from "@/shared/types";
import {
  calculateFittedCanvasDimensions,
  shouldResize,
  scaleCanvasObjects,
} from "@/lib/fabric/rendering";

const handleCroppedCanvasResize = (
  canvas: Canvas,
  container: HTMLElement,
  cropData: CropData,
  setCropData?: (data: CropData) => void
) => {
  if (!cropData?.normalizedCropRect || !cropData.canvasDimensions) return;

  const containerRect = container.getBoundingClientRect();
  const currentCanvasWidth = canvas.getWidth();
  const currentCanvasHeight = canvas.getHeight();

  const backgroundImage = canvas.backgroundImage;
  if (!backgroundImage) return;

  const imageWidth = backgroundImage.width || 512;
  const imageHeight = backgroundImage.height || 512;
  const imageScale = backgroundImage.scaleX || 1;
  const scaledImageWidth = imageWidth * imageScale;
  const scaledImageHeight = imageHeight * imageScale;
  const cropLeft = cropData.normalizedCropRect.left * scaledImageWidth;
  const cropTop = cropData.normalizedCropRect.top * scaledImageHeight;
  const cropWidth = cropData.normalizedCropRect.width * scaledImageWidth;
  const cropHeight = cropData.normalizedCropRect.height * scaledImageHeight;
  // Use the same scaling logic as the original crop operation
  const scale = Math.min(containerRect.width / cropWidth, containerRect.height / cropHeight);
  const actualWidth = cropWidth * scale;
  const actualHeight = cropHeight * scale;

  console.log("Resize check:", {
    current: { width: currentCanvasWidth, height: currentCanvasHeight },
    target: { width: actualWidth, height: actualHeight },
    shouldResize: shouldResize(currentCanvasWidth, currentCanvasHeight, actualWidth, actualHeight),
  });

  if (!shouldResize(currentCanvasWidth, currentCanvasHeight, actualWidth, actualHeight)) {
    return;
  }

  // // Preserve background image transformation properties
  // const currentAngle = backgroundImage.angle || 0;
  // const currentFlipX = backgroundImage.flipX || false;
  // const currentFlipY = backgroundImage.flipY || false;

  // For rotated images (90° or 270°), we need to swap the dimensions
  const bgAngle = backgroundImage.angle || 0;
  if (Math.abs(bgAngle) === 90 || Math.abs(bgAngle) === 270) {
    canvas.setDimensions({ width: actualHeight, height: actualWidth });
  } else {
    canvas.setDimensions({ width: actualWidth, height: actualHeight });
  }
  const vpt: [number, number, number, number, number, number] = [
    scale,
    0,
    0,
    scale,
    -cropLeft * scale,
    -cropTop * scale,
  ];
  canvas.setViewportTransform(vpt);

  // Restore background image transformation properties
  // backgroundImage.angle = currentAngle;
  // backgroundImage.flipX = currentFlipX;
  // backgroundImage.flipY = currentFlipY;

  canvas.renderAll();

  if (setCropData) {
    const updatedCropData = {
      ...cropData,
      canvasDimensions: {
        width: containerRect.width,
        height: containerRect.height,
      },
    };
    setCropData(updatedCropData);
  }
};

const handleOriginalCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();

  const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
    currentWidth,
    currentHeight,
    containerRect.width,
    containerRect.height
  );

  const { width: targetWidth, height: targetHeight } = {
    width: Math.max(fittedWidth, 300),
    height: Math.max(fittedHeight, 200),
  };
  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });
  // canvas.renderOnAddRemove = false;

  scaleCanvasObjects(canvas, imageScale);

  const bgImg = canvas.backgroundImage;
  if (bgImg) {
    const currentAngle = bgImg.angle || 0;
    const currentFlipX = bgImg.flipX || false;
    const currentFlipY = bgImg.flipY || false;

    bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
    bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;

    bgImg.set({
      left: actualWidth / 2,
      top: actualHeight / 2,
      originX: "center",
      originY: "center",
    });

    bgImg.angle = currentAngle;
    bgImg.flipX = currentFlipX;
    bgImg.flipY = currentFlipY;
  }

  // canvas.renderOnAddRemove = true;
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({
  fabricCanvas,
  containerRef,
  cropData,
  setCropData,
}: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    if (cropData?.isCropped && cropData.normalizedCropRect && cropData.canvasDimensions) {
      handleCroppedCanvasResize(canvas, containerRef.current, cropData, setCropData);
    } else {
      handleOriginalCanvasResize(canvas, containerRef.current);
    }
  }, [fabricCanvas, containerRef, cropData, setCropData]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
