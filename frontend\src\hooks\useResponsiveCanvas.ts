import { Canvas } from "fabric";
import { useCallback, useEffect, useRef } from "react";
import { UseResponsiveCanvasProps } from "@/shared/types";
import {
  calculateFittedCanvasDimensions,
  shouldResize,
  scaleCanvasObjects,
} from "@/lib/fabric/rendering";

const handleCanvasResize = (canvas: Canvas, container: HTMLElement) => {
  const containerRect = container.getBoundingClientRect();
  const currentWidth = canvas.getWidth();
  const currentHeight = canvas.getHeight();
  const { width: fittedWidth, height: fittedHeight } = calculateFittedCanvasDimensions(
    currentWidth,
    currentHeight,
    containerRect.width,
    containerRect.height
  );

  const { width: targetWidth, height: targetHeight } = {
    width: Math.max(fittedWidth, 300),
    height: Math.max(fittedHeight, 200),
  };
  if (!shouldResize(currentWidth, currentHeight, targetWidth, targetHeight)) {
    return;
  }

  const scaleX = targetWidth / currentWidth;
  const scaleY = targetHeight / currentHeight;
  const imageScale = Math.min(scaleX, scaleY);
  const actualWidth = currentWidth * imageScale;
  const actualHeight = currentHeight * imageScale;

  canvas.setDimensions({ width: actualWidth, height: actualHeight });

  scaleCanvasObjects(canvas, imageScale);

  const bgImg = canvas.backgroundImage;
  if (bgImg) {
    const currentAngle = bgImg.angle || 0;
    const currentFlipX = bgImg.flipX || false;
    const currentFlipY = bgImg.flipY || false;

    bgImg.scaleX = (bgImg.scaleX || 1) * imageScale;
    bgImg.scaleY = (bgImg.scaleY || 1) * imageScale;

    bgImg.set({
      left: actualWidth / 2,
      top: actualHeight / 2,
      originX: "center",
      originY: "center",
    });

    bgImg.angle = currentAngle;
    bgImg.flipX = currentFlipX;
    bgImg.flipY = currentFlipY;
  }
  canvas.requestRenderAll();
};

export const useResponsiveCanvas = ({ fabricCanvas, containerRef }: UseResponsiveCanvasProps) => {
  const resizeObserverRef = useRef<ResizeObserver | null>(null);

  const resizeCanvas = useCallback(() => {
    if (!fabricCanvas?.current || !containerRef.current) return;

    const canvas = fabricCanvas.current;

    handleCanvasResize(canvas, containerRef.current);
  }, [fabricCanvas, containerRef]);

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    resizeObserverRef.current = new ResizeObserver(() => {
      resizeCanvas();
    });

    resizeObserverRef.current.observe(container);

    return () => {
      resizeObserverRef.current?.disconnect();
      resizeObserverRef.current = null;
    };
  }, [containerRef, resizeCanvas]);

  return {
    resizeCanvas,
  };
};
