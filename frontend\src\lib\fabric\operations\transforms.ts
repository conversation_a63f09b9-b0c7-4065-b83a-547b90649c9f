import { Canvas } from "fabric";
import { TransformState } from "@/shared/types";

export const applyCanvasRotation = (canvas: Canvas, onRotationComplete?: () => void): void => {
  const currentWidth = canvas.width!;
  const currentHeight = canvas.height!;

  canvas.setDimensions({
    width: currentHeight,
    height: currentWidth,
  });

  const canvasElement = canvas.getElement();
  if (canvasElement) {
    canvasElement.width = currentHeight;
    canvasElement.height = currentWidth;
    canvasElement.style.width = `${currentHeight}px`;
    canvasElement.style.height = `${currentWidth}px`;
  }

  if (canvas.backgroundImage) {
    const backgroundImage = canvas.backgroundImage;
    const currentAngle = backgroundImage.angle || 0;
    const newAngle = (currentAngle + 90) % 360;

    backgroundImage.rotate(newAngle);
    backgroundImage.set({
      left: currentHeight / 2,
      top: currentWidth / 2,
      originX: "center",
      originY: "center",
    });

    canvas.backgroundImage = backgroundImage;
  }

  const objects = canvas.getObjects();
  const oldCenterX = currentWidth / 2;
  const oldCenterY = currentHeight / 2;
  const newCenterX = currentHeight / 2;
  const newCenterY = currentWidth / 2;

  objects.forEach((obj) => {
    const objCenterX = obj.left! + (obj.width! * obj.scaleX!) / 2;
    const objCenterY = obj.top! + (obj.height! * obj.scaleY!) / 2;
    const relativeX = objCenterX - oldCenterX;
    const relativeY = objCenterY - oldCenterY;

    const newRelativeX = -relativeY;
    const newRelativeY = relativeX;
    const newObjCenterX = newCenterX + newRelativeX;
    const newObjCenterY = newCenterY + newRelativeY;

    obj.set({
      left: newObjCenterX - (obj.width! * obj.scaleX!) / 2,
      top: newObjCenterY - (obj.height! * obj.scaleY!) / 2,
      angle: (obj.angle || 0) + 90,
    });
  });

  canvas.requestRenderAll();

  // Trigger resize callback if provided
  if (onRotationComplete) {
    onRotationComplete();
  }
};

export const applyCanvasFlipHorizontal = (canvas: Canvas): void => {
  if (canvas.backgroundImage) {
    const currentFlipX = canvas.backgroundImage.flipX || false;
    canvas.backgroundImage.set("flipX", !currentFlipX);
  }
  canvas.forEachObject((obj) => {
    const currentFlipX = obj.flipX || false;
    obj.set({
      flipX: !currentFlipX,
      left: canvas.width! - obj.left! - obj.width! * obj.scaleX!,
    });
  });
  canvas.requestRenderAll();
};

export const applyCanvasFlipVertical = (canvas: Canvas): void => {
  if (canvas.backgroundImage) {
    const currentFlipY = canvas.backgroundImage.flipY || false;
    canvas.backgroundImage.set("flipY", !currentFlipY);
  }
  canvas.forEachObject((obj) => {
    const currentFlipY = obj.flipY || false;
    obj.set({
      flipY: !currentFlipY,
      top: canvas.height! - obj.top! - obj.height! * obj.scaleY!,
    });
  });
  canvas.requestRenderAll();
};

export const createRotateHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>,
  onRotationComplete?: () => void
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current, onRotationComplete);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };
};

export const createFlipHorizontalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipHorizontal(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };
};

export const createFlipVerticalHandler = (
  fabricCanvas: React.RefObject<Canvas | null>,
  setTransformState: React.Dispatch<React.SetStateAction<TransformState>>
) => {
  return () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipVertical(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };
};
