import { useState } from "react";
import { Canvas } from "fabric";
import { TransformState, ImageTransformsState } from "@/shared/types";
import {
  applyCanvasRotation,
  applyCanvasFlipHorizontal,
  applyCanvasFlipVertical,
} from "@/lib/fabric/operations/transforms";

export const useImageTransforms = (
  fabricCanvas: React.RefObject<Canvas | null>,
  initialTransformState: TransformState = {
    rotations: 0,
    flipHorizontal: false,
    flipVertical: false,
  },
  onRotationComplete?: () => void
): ImageTransformsState => {
  const [transformState, setTransformState] = useState<TransformState>(initialTransformState);

  const handleRotate = () => {
    if (fabricCanvas?.current) {
      applyCanvasRotation(fabricCanvas.current, onRotationComplete);
      setTransformState((prev) => ({ ...prev, rotations: (prev.rotations + 1) % 4 }));
    }
  };

  const handleFlipHorizontal = () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipHorizontal(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipHorizontal: !prev.flipHorizontal }));
    }
  };

  const handleFlipVertical = () => {
    if (fabricCanvas?.current) {
      applyCanvasFlipVertical(fabricCanvas.current);
      setTransformState((prev) => ({ ...prev, flipVertical: !prev.flipVertical }));
    }
  };

  const applySavedTransforms = () => {
    if (!fabricCanvas.current) return;

    const canvas = fabricCanvas.current;

    for (let i = 0; i < transformState.rotations; i++) {
      applyCanvasRotation(canvas, onRotationComplete);
    }

    if (transformState.flipHorizontal) {
      applyCanvasFlipHorizontal(canvas);
    }

    if (transformState.flipVertical) {
      applyCanvasFlipVertical(canvas);
    }
  };

  return {
    transformState,
    setTransformState,
    handleRotate,
    handleFlipHorizontal,
    handleFlipVertical,
    applySavedTransforms,
  };
};
